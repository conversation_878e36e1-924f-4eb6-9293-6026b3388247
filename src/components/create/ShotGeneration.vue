<template>
  <div class="result-content" v-if="animationResult">

    <AXThoughtChain v-if="animationResult.status === 'in-progress'" :loading="true" :thinking="true">
      <div class="thought-content">正在生成分镜，请稍候...</div>
    </AXThoughtChain>

    <div class="shot-designs" ref="shotDesignsRef">
      <!-- 章节列表视图 - 使用Grid布局 -->
      <div v-if="!selectedSegmentId" class="chapters-grid-container">
        <div v-for="(group, groupIndex) in shotChapters" :key="groupIndex" class="chapter-card"
          :class="{ 'offset-card': groupIndex % 2 !== 0 }" @click="toggleSegment(group.segmentId)">
          <div class="book-spine"></div>
          <div class="book-cover" :class="{ 'has-cover-image': group.firstShotImage && !group.coverImageError }"
            :style="group.firstShotImage && !group.coverImageError ? { backgroundImage: `url(${group.firstShotImage})` } : {}">
            <!-- 添加详情按钮 -->
            <div class="chapter-detail-button" @click.stop="showChapterPrompt(group)">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="cover-overlay" v-if="group.firstShotImage && !group.coverImageError"></div>
            <div class="chapter-number">章节 {{ group.segmentId }}</div>
            <div class="chapter-title">{{ group.segmentName }}</div>
            <div class="chapter-stats">
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
                <div class="stat-value">{{ group.totalShotCount || 0 }} 分镜</div>
              </div>
              <div class="stat-item">
                <div class="stat-icon" :class="{ 'completed': getSegmentCompletion(group.segmentId).isComplete }">
                  <el-icon>
                    <Check />
                  </el-icon>
                </div>
                <div class="stat-value">{{ getSegmentCompletion(group.segmentId).isComplete ? '已完成' : '未完成' }}</div>
              </div>
              <!-- 高级创作按钮 - 仅在章节状态为已完成时显示 v-if="getSegmentCompletion(group.segmentId).isComplete"  -->
              <!-- @click.stop="handleAdvancedCreation(group)" -->
              <div class="advanced-creation-button">
                <el-icon><VideoPlay /></el-icon>
                <span>查看分镜</span>
              </div>
            </div>
            <!-- 隐藏的图片用于预加载和错误处理 -->
            <img v-if="group.firstShotImage" :src="`${group.firstShotImage}?x-oss-process=image/resize,w_280`" class="hidden-preload-image"
              @error="handleCoverImageError(group)" />
          </div>
        </div>
        
        <!-- 添加章节卡片 -->
        <div class="chapter-card add-chapter-card" @click="openAddChapterDialog">
          <div class="book-spine add-chapter-spine"></div>
          <div class="book-cover add-chapter-cover">
            <div class="add-chapter-icon">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="chapter-title">添加新章节</div>
            <div class="add-chapter-desc">点击创建新的故事章节</div>
          </div>
        </div>
      </div>

      <!-- 分镜内容视图 - 当选择了章节时显示 -->
      <div v-if="selectedSegmentId" class="scene-content-view">
        <div class="back-button" @click="closeSceneContent">
          <el-icon>
            <Back />
          </el-icon>
          <span>返回章节列表</span>
        </div>

        <div class="selected-chapter-header">
          <div class="selected-chapter-title">
            {{ getSelectedChapterName() }}
          </div>
          <div class="chapter-actions" v-if="false">
            <div class="scene-group-icon-btn" v-if="!getSegmentCompletion(selectedSegmentId).isComplete"
              @click.stop="insertTextSendMessage('完善章节' + selectedSegmentId + ' 剩余内容')">
              完善章节内容
            </div>

            <el-tooltip :content="getSegmentCompletion(selectedSegmentId).message" placement="top" :effect="'light'">
              <div v-if="getSegmentCompletion(selectedSegmentId).isComplete" class="scene-group-icon-btn"
                @click.stop="insertTextSendMessage('调用工具生成章节【chapterID:' + selectedSegmentId + '】有声故事')">
                生成有声故事
              </div>
              <div v-else class="scene-group-icon-btn disabled">
                待完成
              </div>
            </el-tooltip>
          </div>
          <div class="scene-group-icon-btn" @click.stop="handleAdvancedCreationSelectedSegmentId()">
            <el-icon><VideoPlay /></el-icon>
            <span>创作视频</span>
          </div>
        </div>
      </div>

      <!-- 使用过渡动画，显示SceneContent组件 -->
      <transition name="scene-toggle">
        <SceneContent :shots="shotShots" :conversationId="conversationId" :columnCount="columnCount"
          @image-error="handleImageError" @update:shots="updateSceneShots" :imageSize="imageSize" />
      </transition>

    </div>

    <!-- 分镜详情对话框 -->
    <el-dialog v-model="showingDetails" :title="dialogTitle" :show-close="true" width="1100px" @close="hideShotDetails"
      destroy-on-close>
      <div v-if="currentShotData">
        <div class="detail-dialog-content">
          <!-- 添加场景名称信息 -->
          <div class="scene-info-bar">
            <div class="scene-info-label">所属场景:</div>
            <div class="scene-info-value">{{ currentSceneName }}</div>
          </div>

          <!-- 左右两栏布局 -->
          <div class="detail-layout">
            <!-- 左侧：图片 -->
            <div class="detail-image-container">
              <img :src="currentShotData.image" alt="分镜图片" class="detail-image">
            </div>

            <!-- 右侧：详情内容 -->
            <div class="detail-content">
              <div class="detail-card">
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon>
                      <Picture />
                    </el-icon>
                    <span>构图</span>
                  </div>
                  <div class="detail-value">
                    {{ currentShotData?.composition || '未设置' }}
                  </div>
                </div>

                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon>
                      <VideoPlay />
                    </el-icon>
                    <span>镜头类型</span>
                  </div>
                  <div class="detail-value shot-type-value">
                    {{ currentShotData?.type || '未设置' }}
                  </div>
                </div>

                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon>
                      <ArrowRight />
                    </el-icon>
                    <span>运动</span>
                  </div>
                  <div class="detail-value shot-movement-value">
                    {{ currentShotData?.movement || '未设置' }}
                  </div>
                </div>
                <div class="detail-label character-label">
                  <el-icon>
                    <User />
                  </el-icon>
                  <span>角色</span>
                </div>
                <div class="character-list">
                  <div v-if="currentShotData?.characters && currentShotData.characters.length > 0"
                    class="character-list-container">
                    <div v-for="(character, i) in currentShotData.characters" :key="i" class="multi-value-item">
                      <span class="multi-value-tag character-tag">{{ character }}</span>
                    </div>
                  </div>
                  <div v-else class="empty-characters">
                    暂无角色信息
                  </div>
                </div>
              </div>

              <!-- 添加对话内容区域 -->
              <div class="detail-card dialogue-card" v-if="currentShotData?.lines">
                <div class="detail-label dialogue-label">
                  <el-icon>
                    <ChatDotRound />
                  </el-icon>
                  <span>对话内容</span>
                </div>
                <div class="dialogue-content-container">
                  <div v-for="(line, character) in currentShotData.lines" :key="character" class="dialogue-item">
                    <span class="dialogue-character" v-if="line.split(':').length > 1">{{ line.split(':')[0] }}</span>
                    <span class="dialogue-character" v-else>未知</span>
                    <span class="dialogue-content" v-if="line.split(':').length > 1">（{{ extractAction(line) }}）{{
                      extractSpeech(line) }}</span>
                    <span class="dialogue-content" v-else>{{ extractSpeech(line) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>

  <!-- 高级创作确认对话框 -->
  <el-dialog v-model="showCanvasConfirmDialog" :title="confirmDialogTitle" width="450px" destroy-on-close custom-class="canvas-confirm-dialog">
    <div class="confirm-dialog-content">
      <!-- <div class="confirm-dialog-icon">
        <el-icon><WarningFilled /></el-icon>
      </div> -->
      <div class="confirm-dialog-message">
        <p>{{ confirmDialogMessage }}</p>
        <!-- <div class="confirm-dialog-options">
          <div class="option-item">
            <div class="option-title">覆盖存在画布</div>
            <div class="option-desc">创建全新画布，之前的编辑将被覆盖</div>
          </div>
          <div class="option-item">
            <div class="option-title">使用存在画布</div>
            <div class="option-desc">继续编辑之前的画布内容</div>
          </div>
        </div> -->
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelOverwrite" :disabled="isProcessing" class="overwrite-button">
          <!-- <el-icon><WarningFilled /></el-icon> -->
          取消
        </el-button>
        <el-button type="warning" @click="confirmOverwrite" :loading="isProcessing" class="keep-button">
          <el-icon v-if="!isProcessing"><Check /></el-icon>
          新增草稿
        </el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 添加章节对话框 -->
  <el-dialog v-model="showAddChapterDialog" title="新增章节" width="600px" destroy-on-close custom-class="add-chapter-dialog">
    <div class="add-chapter-dialog-content">
      <div class="add-chapter-description">
        <div class="description-title">请输入新章节的具体需求</div>
        <div class="description-subtitle">AI将根据您的描述创建章节内容</div>
      </div>
      <el-input
        v-model="chapterRequirement"
        type="textarea"
        :rows="6"
        placeholder="例如：一个发生在未来太空站的章节，描述主角在失重环境下的冒险"
        resize="none"
        class="chapter-input"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelAddChapter" :disabled="isAddingChapter" class="cancel-button">
          取消
        </el-button>
        <el-button type="primary" @click="handleAddChapter" :loading="isAddingChapter" class="confirm-button">
          <el-icon v-if="!isAddingChapter"><Plus /></el-icon>
          添加章节
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 章节提示词对话框 -->
  <ChapterPrompt 
    v-model:visible="showPromptDialog" 
    :segmentId="currentPromptSegmentId" 
    :chapterName="currentPromptChapterName"
  />
</template>

<script setup>
import { ref, computed, reactive, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import { ThoughtChain as AXThoughtChain } from 'ant-design-x-vue'
import { ElDialog, ElTooltip, ElMessage, ElInput } from 'element-plus'
import {
  ArrowRight,
  Picture,
  User,
  VideoPlay,
  ChatDotRound,
  Close,
  Check,
  Back,
  WarningFilled,
  Plus,
  InfoFilled,
} from '@element-plus/icons-vue'
import SceneContent from './SceneContent.vue'
import ChapterPrompt from './ChapterPrompt.vue'
import { clearContext, getShotChapters, getShotShots, updateShot, getChapterCanvasDetail, convertChapterToShot } from '@/api/auth.js'
import { useRouter } from 'vue-router'

// 初始化路由
const router = useRouter()

const props = defineProps({
  animationResult: {
    type: Object,
    default: null
  },
  shotShots: {
    type: Object,
    default: () => ({ shotGroups: [] })
  },
  conversationId: {
    type: String,
    default: ''
  },
  imageSize: {
    type: String,
    default: '9:16'
  },
  activeTab: {
    type: String,
    required: true
  }
})

// [
//   {
//     "id": 4,
//     "segmentId": "C01",
//     "segmentName": "伊卡洛斯号的阴影",
//     "sceneCount": 5,
//     "createTime": "2025-06-18 16:24:11",
//     "updateTime": "2025-06-18 16:24:11",
//     "totalShotCount": 19,
//     "completedImageCount": 14,
//     "completedVoiceCount": 19,
//     "completedNarrationCount": 19,
//     "isAllCompleted": false
//   }
// ]
const shotChapters = ref()

// 当前选中的章节ID
const selectedSegmentId = ref(null)

// 分镜列表
const shotShots = ref([])
// 查询章节列表
const getChapters = async () => {
  const response = await getShotChapters(props.conversationId)
  console.log('getShotChapters', response)
  if (response.data) {
    shotChapters.value = response.data

    if (selectedSegmentId.value) {
      getShots()
    }
  }
}

watch(() => shotChapters.value, (newValue) => {
  console.log('ShotGeneration中shotChapters变化为:', newValue)
  emit('update-shot-shots-count', shotChapters.value.length)
})

// 轮询定时器
let pollingTimer = null

// 查询分镜列表
const getShots = async () => {
  const response = await getShotShots(props.conversationId, selectedSegmentId.value)
  console.log('getShotShots', response)
  if (response.data) {
    shotShots.value = response.data

    // 检查是否有 PENDING_SUBMISSION 状态的分镜
    const hasPendingSubmission = shotShots.value.some(item => item.imageStatus == 'PENDING_SUBMISSION' || item.imageStatus == 'PROCESSING')

    if (hasPendingSubmission) {
      // 如果有待提交的分镜，开始轮询
      startPolling()
    } else {
      // 如果没有待提交的分镜，停止轮询
      stopPolling()
    }
  }
}

// 开始轮询
const startPolling = () => {
  // 清除之前的定时器
  stopPolling()

  // 设置5秒后再次查询
  pollingTimer = setTimeout(() => {
    if (selectedSegmentId.value) {
      getShots()
    }
  }, 5000)
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearTimeout(pollingTimer)
    pollingTimer = null
  }
}

// 修改分镜数据
const updateShotData = async (data) => {
  const response = await updateShot(data)
  console.log('updateShot', response)
  if (response.success && response.data) {
    // 更新分镜数据
    getShots()
  }
}

// 定义emit事件
const emit = defineEmits(['image-error', 'update:shotShots', 'update-shot-shots-count'])

// 定义列数变量
const columnCount = ref(1)

const shotDesignsRef = ref(null) // 容器引用
const containerWidth = ref(0) // 容器宽度

// 场景折叠状态，默认所有场景都是折叠状态
const collapsedScenes = reactive({})

// 章节折叠状态，默认所有章节都是折叠状态
const collapsedSegments = reactive({})

// 存储上一次的分镜数据，用于比较变化
const previousShotGroups = ref([])

// 查找场景变化并返回变化的场景ID
const findChangedScenes = (newGroups, oldGroups) => {
  const changedSceneIds = new Set()

  // 如果是首次加载，返回空数组
  if (!oldGroups || oldGroups.length === 0) {
    return []
  }

  // 创建旧场景的映射，以便快速查找
  const oldScenesMap = {}
  oldGroups.forEach(group => {
    if (group.scene_id) {
      // 存储场景ID和对应的shots数组的JSON字符串，用于后续比较
      oldScenesMap[group.scene_id] = JSON.stringify(group.shots || [])
    }
  })

  // 检查新场景是否存在变化
  newGroups.forEach(group => {
    if (group.scene_id) {
      const newShotsStr = JSON.stringify(group.shots || [])

      // 如果场景是新增的，或者场景内容发生变化，则标记为变化的场景
      if (!oldScenesMap[group.scene_id] || oldScenesMap[group.scene_id] !== newShotsStr) {
        changedSceneIds.add(group.scene_id)
      }
    }
  })

  return Array.from(changedSceneIds)
}

// 初始化所有场景为折叠状态，除第一个场景外
const initializeCollapsedState = () => {
  // 判断是否有多个场景组
  if (props.shotShots?.shotGroups && props.shotShots.shotGroups.length > 1) {
    // 先将所有场景设置为折叠状态
    // 遍历场景组
    props.shotShots.shotGroups.forEach(group => {
      // 如果场景组中有场景ID
      if (group.scene_id) {
        // 将对应场景ID的折叠状态设置为true
        collapsedScenes[group.scene_id] = true
      }

      // 章节初始化折叠状态
      if (group.segment_id) {
        // 将所有章节默认设置为折叠
        collapsedSegments[group.segment_id] = true
      }
    })

    // 找到第一个章节ID并展开
    if (props.shotShots.shotGroups.length > 0) {
      const firstSegmentId = props.shotShots.shotGroups[0].segment_id
      if (firstSegmentId) {
        collapsedSegments[firstSegmentId] = false
      }
    }
  }
}

// 切换章节折叠状态
const toggleSegment = (segmentId) => {
  // 设置当前选中的章节ID
  selectedSegmentId.value = segmentId
  // 获取该章节的分镜数据
  getShots()
}

// 关闭分镜内容，返回章节列表
const closeSceneContent = () => {
  selectedSegmentId.value = null
  shotShots.value = []
  // 停止轮询
  stopPolling()
  getChapters()
}

// 获取当前选中章节的名称
const getSelectedChapterName = () => {
  if (!selectedSegmentId.value) return ''
  const chapter = shotChapters.value.find(chapter => chapter.segmentId === selectedSegmentId.value)
  return chapter ? `章节 ${chapter.segmentId} - ${chapter.segmentName}` : ''
}

// 组件挂载后初始化折叠状态
onMounted(() => {
  initializeCollapsedState()
  // 加载章节列表
  getChapters()

  // 如果有默认展开的场景，发出场景ID变更事件
  if (props.shotShots?.shotGroups && props.shotShots.shotGroups.length > 0) {
    const firstGroup = props.shotShots.shotGroups[0]
    if (firstGroup && firstGroup.scene_id) {
      collapsedScenes[firstGroup.scene_id] = false

      // 展开第一个章节
      if (firstGroup.segment_id) {
        collapsedSegments[firstGroup.segment_id] = false
      }

      window.dispatchEvent(new CustomEvent('shot-scene-id-changed', {
        detail: { sceneId: firstGroup.scene_id }
      }));
    }
  }

  // 初始化上一次的分镜数据
  previousShotGroups.value = JSON.parse(JSON.stringify(props.shotShots?.shotGroups || []))

  // 监听AI修图完成事件
  window.addEventListener('ai-image-updated', handleAIImageUpdate);
  // 监听打开高级创作事件
  window.openAdvancedCreation = openAdvancedCreation;
})

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  // 移除AI修图事件监听
  window.removeEventListener('ai-image-updated', handleAIImageUpdate);
  window.openAdvancedCreation = null;
  // 清理轮询定时器
  stopPolling();
});

// 当前展开的分镜索引 [groupIndex, shotIndex]
const expandedShot = ref([0, 0])

// 判断是否需要使用折叠功能（分镜总数量超过10个时使用）
const shouldUseCollapse = computed(() => {
  if (!props.shotShots.shotGroups) return false

  let totalShots = 0
  props.shotShots.shotGroups.forEach(group => {
    totalShots += group.shots?.length || 0
  })

  return totalShots > 10
})

// 处理图片加载失败
const handleImageError = (shotId) => {
  console.log('处理图片加载失败:', shotId)

  // 查找失败的图片所在的分镜并标记
  props.shotShots.shotGroups?.forEach(group => {
    group.shots?.forEach(shot => {
      if (shot.id === shotId) {
        shot.imageError = true
      }
    })
  })
}

// 处理封面图片加载错误
const handleCoverImageError = (chapter) => {
  console.log('处理封面图片加载失败:', chapter.segmentId)
  chapter.coverImageError = true
}

// 处理AI修图更新事件
const handleAIImageUpdate = (event) => {
  if (!event.detail) return;

  const { originalUrl, newUrl } = event.detail;
  console.log('分镜组件收到AI修图更新事件:', originalUrl, '->', newUrl);

  // 更新分镜列表 shotShots 中图片
  if (shotShots.value) {
    shotShots.value.forEach(shot => {
      if (shot.image === originalUrl) {
        console.log('更新分镜图片:', shot.id);
        shot.image = newUrl;
        shot.imageError = false;
        shot.imageStatus = 'COMPLETED'
      }
    });
  }

  // 更新分镜图片
  if (props.shotShots && props.shotShots.shotGroups) {
    props.shotShots.shotGroups.forEach(group => {
      if (group.shots) {
        group.shots.forEach(shot => {
          if (shot.image === originalUrl) {
            console.log('更新分镜图片:', shot.id, 'in scene:', group.scene_id);
            shot.image = newUrl;
            shot.imageError = false;
            shot.imageStatus = 'COMPLETED'
          }
        });
      }
    });
  }

  // 更新章节封面图片
  if (shotChapters.value) {
    shotChapters.value.forEach(chapter => {
      if (chapter.firstShotImage === originalUrl) {
        console.log('更新章节封面图片:', chapter.segmentId);
        chapter.firstShotImage = newUrl;
        chapter.coverImageError = false;
      }
    });
  }
}

// 更新场景分镜
const updateSceneShots = (updatedShot, shotId, sceneId) => {
  console.log('updateSceneShot', updatedShot, shotId, sceneId)
  if (!props.shotShots || !props.shotShots.shotGroups) return;

  updateShotData(updatedShot)

  // // 创建一个深拷贝，避免直接修改props
  // const updatedShotShots = JSON.parse(JSON.stringify(props.shotShots));

  // // 找到对应的场景并更新其对应的 shot
  // const sceneIndex = updatedShotShots.shotGroups.findIndex(group => group.scene_id === sceneId);
  // if (sceneIndex !== -1) {
  //   const shotIndex = updatedShotShots.shotGroups[sceneIndex].shots.findIndex(shot => shot.id === updatedShot.id);
  //   if (shotIndex !== -1) {
  //     updatedShotShots.shotGroups[sceneIndex].shots[shotIndex] = updatedShot;
  //   }
  //   // 触发更新
  //   emit('update:shotShots', updatedShotShots);
  // }
}


const insertTextSendMessage = async (text) => {
  // 先清空上下文
  if (props.conversationId) {
    try {
      const response = await clearContext(props.conversationId);
      console.log('上下文已清空:', response);
    } catch (error) {
      console.error('清空上下文失败:', error);
    }
  }

  // 然后发送消息
  if (window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
    window.insertTextSendMessage(text);
  }
}

// 提取动作描述
const extractAction = (line) => {
  const match = line.match(/\((.*?)\)/);
  return match ? match[1] : '';
}

// 提取对话内容
const extractSpeech = (line) => {
  return line.replace(/.*?[：:]?\s*(\(.*?\))?\s*/, '');
}

// 显示分镜详情对话框
const showingDetails = ref(false);
const currentDetailShot = ref([-1, -1]);
const dialogTitle = ref('分镜详情');
const currentSceneName = ref('');

// 获取当前选中分镜数据
const currentShotData = computed(() => {
  if (currentDetailShot.value[0] < 0 || currentDetailShot.value[1] < 0) return null;

  const group = props.shotShots.shotGroups[currentDetailShot.value[0]];
  if (!group || !group.shots) return null;

  return group.shots[currentDetailShot.value[1]] || null;
});

// 隐藏分镜详情
const hideShotDetails = () => {
  showingDetails.value = false;
  currentDetailShot.value = [-1, -1];
}

// 计算场景组的分镜完成状态
const getSceneCompletion = (group) => {
  if (!group || !group.shots || group.shots.length === 0) {
    return {
      total: 0,
      imageCompleted: 0,
      narrationCompleted: 0,
      voiceCompleted: 0,
      fullyCompleted: 0,
      status: 'incomplete', // 'complete', 'partial', 'incomplete'
      message: '暂无分镜'
    }
  }

  const total = group.shots.length
  let imageCompleted = 0
  let narrationCompleted = 0
  let voiceCompleted = 0
  let fullyCompleted = 0

  group.shots.forEach(shot => {
    // 检查图片
    const hasImage = shot.image && (!shot.imageStatus || shot.imageStatus === 'COMPLETED')
    // 检查旁白
    const hasNarration = !!shot.narration
    // 检查声音
    const hasVoice = !!shot.voice

    if (hasImage) imageCompleted++
    if (hasNarration) narrationCompleted++
    if (hasVoice) voiceCompleted++
    if (hasImage && hasNarration && hasVoice) fullyCompleted++
  })

  // 计算状态
  let status = 'incomplete'
  if (fullyCompleted === total) {
    status = 'complete'
  } else if (imageCompleted > 0 || narrationCompleted > 0 || voiceCompleted > 0) {
    status = 'partial'
  }

  // 生成消息
  let message = `总计 ${total} 个分镜  \n`
  message += `「图片: ${imageCompleted}/${total} 」\n`
  message += `「旁白: ${narrationCompleted}/${total} 」\n`
  message += `「声音: ${voiceCompleted}/${total} 」`

  return {
    total,
    imageCompleted,
    narrationCompleted,
    voiceCompleted,
    fullyCompleted,
    status,
    message
  }
}

// 计算章节的完成状态
// 分镜总数 totalShotCount
// 分镜图片完成数量 completedImageCount
// 分镜声音完成数量 completedVoiceCount
// 分镜旁白完成数量 completedNarrationCount
const getSegmentCompletion = (groupOrId) => {
  // 如果没有传入参数，返回默认值
  if (!groupOrId) return {
    isComplete: false,
    totalScenes: 0,
    message: '章节不存在'
  }

  // 处理传入的是ID字符串的情况
  if (typeof groupOrId === 'string') {
    // 根据ID查找对应的章节
    const chapter = shotChapters.value.find(c => c.segmentId === groupOrId)
    if (!chapter) return {
      isComplete: false,
      totalScenes: 0,
      message: '章节不存在'
    }
    // 使用找到的章节对象
    groupOrId = chapter
  }

  var totalShotCount = groupOrId.totalShotCount || 0
  var completedImageCount = groupOrId.completedImageCount || 0
  var completedVoiceCount = groupOrId.completedVoiceCount || 0
  var completedNarrationCount = groupOrId.completedNarrationCount || 0
  // "totalShotCount": 19,
  // "completedImageCount": 14,
  // "completedVoiceCount": 19,
  // "completedNarrationCount": 19,

  const isComplete = completedImageCount === totalShotCount // && completedVoiceCount === totalShotCount && completedNarrationCount === totalShotCount
  const totalScenes = totalShotCount
  let message = `总计 ${totalScenes} 个分镜  \n`
  message += `「图片:${completedImageCount}/${totalScenes}」\n`
  message += `「旁白:${completedNarrationCount}/${totalScenes}」\n`
  message += `「声音:${completedVoiceCount}/${totalScenes} 」`
  return {
    isComplete,
    totalScenes,
    message
  }
}

// 处理分镜数据，为每个分镜添加场景信息
const processedShots = (segment_id) => {
  if (!props.shotShots || !props.shotShots.shotGroups) return []

  // 筛选出具有相同 segment_id 的所有 group
  const segmentGroups = props.shotShots.shotGroups.filter(group => group.segment_id === segment_id)

  if (segmentGroups.length === 0) return []

  // 创建一个新数组来存储合并后的 shots
  const mergedShots = []

  // 遍历筛选出的 groups，合并 shots
  segmentGroups.forEach(group => {
    if (!group.shots) return

    // 深拷贝以避免直接修改原始数据
    const shots = JSON.parse(JSON.stringify(group.shots))

    // 为每个分镜添加场景信息
    shots.forEach(shot => {
      shot.sceneName = group.sceneName || ''
      shot.sceneId = group.scene_id || ''
    })

    // 将处理后的 shots 添加到合并数组
    mergedShots.push(...shots)
  })

  return mergedShots
}

const isFirstLoad = ref(true)

watch(() => props.activeTab, (newValue) => {
  console.log('ShotGeneration中activeTab变化为:', newValue)

  const executeTabActions = () => {
    if (newValue === 'animation') {
      // 加载章节列表
      getChapters()
    }
  }

  // 只有第一次加载时延迟执行，避免首次加载时conversationId为null
  if (isFirstLoad.value) {
    setTimeout(() => {
      executeTabActions()
      isFirstLoad.value = false
    }, 300)
  } else {
    executeTabActions()
  }
}, { immediate: true })


// 监听 conversationId 变化，重新加载记录
watch(() => props.conversationId, (newId) => {
  if (newId) {
    getChapters()
  } else {
    // 如果 conversationId 无效，清空记录并通知父组件
    records.value = []
    emit('update-records-count', 0)
  }
}, { immediate: false })

// 高级创作相关状态
const showCanvasConfirmDialog = ref(false)
const processingChapter = ref(null)
const existingCanvasId = ref(null)
const isProcessing = ref(false)
const confirmDialogTitle = ref('高级创作确认')
const confirmDialogMessage = ref('')

// 添加章节相关状态
const showAddChapterDialog = ref(false)
const chapterRequirement = ref('')
const isAddingChapter = ref(false)

const handleAdvancedCreationSelectedSegmentId = () => {
  console.log('handleAdvancedCreationSelectedSegmentId:', selectedSegmentId.value)
  shotChapters.value.forEach(chapter => {
    if (chapter.segmentId === selectedSegmentId.value) {
      handleAdvancedCreation(chapter)
    }
  })
}

// 处理高级创作按钮点击
const handleAdvancedCreation = async (chapter) => {
    // 设置当前处理的章节
    processingChapter.value = chapter
    openAdvancedCreation(chapter.segmentId)
}

// 打开高级创作
const openAdvancedCreation = async (segmentId) => {
  
  try {
    // 查询该章节是否已经有画布
    const response = await getChapterCanvasDetail(props.conversationId, segmentId)
    console.log('getChapterCanvasDetail response:', response)

    if (response.success && response.data && response.data.id) {
      // 已存在画布，显示确认对话框
      existingCanvasId.value = response.data.id
      confirmDialogTitle.value = ''
      confirmDialogMessage.value = `章节已经有草稿，是否确定创建新的草稿？`
      showCanvasConfirmDialog.value = true
    } else {
      // 不存在画布，直接创建
      await createCanvas(segmentId)
    }
  } catch (error) {
    console.error('获取章节画布详情失败:', error)
  }
}

// 创建画布
const createCanvas = async (segmentId) => {
  try {
    // 调用API创建画布
    const response = await convertChapterToShot(props.conversationId, segmentId)
    console.log('convertChapterToShot response:', response)

    if (response.success && response.data) {
      // 创建成功，跳转到画布编辑页面
      router.push({
        path: '/video-editor',
        query: { canvasId: response.data }
      })
    } else {
      throw new Error(response.errMessage || '创建画布失败')
    }
  } catch (error) {
    console.error('创建画布失败:', error)
    ElMessage.error('创建画布失败: ' + (error.message || '未知错误'))
  } finally {
    showCanvasConfirmDialog.value = false
  }
}

// 确认覆盖存在画布
const confirmOverwrite = async () => {
  if (!processingChapter.value) return
  
  try {
    // 调用API创建新画布（覆盖存在画布）
    await createCanvas(processingChapter.value.segmentId)
  } catch (error) {
    console.error('覆盖画布失败:', error)
  }
}

// 取消覆盖，直接跳转到存在画布
const cancelOverwrite = () => {
  // if (existingCanvasId.value) {
  //   router.push({
  //     path: '/video-editor',
  //     query: { canvasId: existingCanvasId.value }
  //   })
  // }
  
  // 重置状态
  showCanvasConfirmDialog.value = false
  processingChapter.value = null
  existingCanvasId.value = null
}

// 打开添加章节对话框
const openAddChapterDialog = () => {
  showAddChapterDialog.value = true
  chapterRequirement.value = ''
}

// 处理添加章节
const handleAddChapter = async () => {
  if (!chapterRequirement.value.trim()) {
    ElMessage.warning('请输入章节需求')
    return
  }
  
  isAddingChapter.value = true
  try {
    await insertTextSendMessage(`增加新章节，具体需求是：${chapterRequirement.value}`)
    showAddChapterDialog.value = false
    chapterRequirement.value = ''
  } catch (error) {
    console.error('添加章节失败:', error)
    ElMessage.error('添加章节失败: ' + (error.message || '未知错误'))
  } finally {
    isAddingChapter.value = false
  }
}

// 取消添加章节
const cancelAddChapter = () => {
  showAddChapterDialog.value = false
  chapterRequirement.value = ''
}

// 章节提示词对话框相关状态
const showPromptDialog = ref(false)
const currentPromptSegmentId = ref('')
const currentPromptChapterName = ref('')

// 显示章节提示词
const showChapterPrompt = (chapter) => {
  currentPromptSegmentId.value = chapter.id
  currentPromptChapterName.value = `章节 ${chapter.segmentId} - ${chapter.segmentName}`
  showPromptDialog.value = true
}

</script>

<style scoped>
.result-content {
  overflow: scroll;
}

.scene-break-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: color 0.3s;
  background-color: #706cefd7;
  padding: 5px 10px;
  border-radius: 50%;
  height: 22px;
}

.scene-group-segment-comint {
  min-width: 32px;
  border-radius: 10px;
  color: white;
  display: flex;
  align-items: center;
  padding: 4px 16px;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  background-color: #8d8ff0;
}

.scene-group-segment-comint:hover {
  background-color: #6366f1;
  transform: scale(1.05);
}

/* 图标按钮容器 */
.scene-group-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 图标按钮样式 */
.scene-group-icon-btn {
  padding: 4px 8px;
  border-radius: 8px;
  background-color: #8d8ff0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.scene-group-icon-btn:hover {
  background-color: #6366f1;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.5);
}

body.dark .scene-group-icon-btn {
  background-color: rgba(99, 102, 241, 0.7);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
}

body.dark .scene-group-icon-btn:hover {
  background-color: rgba(99, 102, 241, 0.9);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.4);
}

.scene-group-icon-btn.disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
  opacity: 0.7;
}

.scene-group-icon-btn.disabled:hover {
  background-color: #a0a0a0;
  transform: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

body.dark .scene-group-icon-btn.disabled {
  background-color: rgba(160, 160, 160, 0.3);
}

body.dark .scene-group-icon-btn.disabled:hover {
  background-color: rgba(160, 160, 160, 0.3);
}

/* 自定义tooltip样式 */
:deep(.el-popper.is-light) {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 6px;
}

body.dark :deep(.el-popper.is-light) {
  background-color: #1e293b;
  color: #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.scene-group-left {
  min-width: 32px;
  border-radius: 10px;
  color: rgba(0, 0, 0, 0.768);
  display: flex;
  align-items: center;
  justify-content: left;
  padding: 3px 10px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 1px 4px #6365f118;
  background: linear-gradient(135deg, #a3a6fb37 0%, #ffffff02 100%);
  margin-bottom: -0px;
}

body.dark .scene-group-left {
  color: rgba(255, 255, 255, 0.768);
}

.character-list-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.shot-designs {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 6px;
  overflow: scroll;
  overflow-x: hidden;
  transition: background-color 0.3s;
}

.shot-designs::-webkit-scrollbar {
  width: 4px;
}

.shot-designs::-webkit-scrollbar-thumb {
  background-color: #6365f139;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .shot-designs::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.2);
}

.shot-designs::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .shot-designs::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 场景分组样式 */
.scene-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* margin-bottom: 4px; */
}

.scene-group-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.scene-group-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.scene-group-header2 {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  color: var(--text-secondary);
  cursor: pointer;
  transition: transform 0.3s ease;
  padding: 10px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 12px #6365f152;
  background: linear-gradient(135deg, #9396fb52 0%, #fff 100%);
  gap: 10px;
}

body.dark .scene-group-header2 {
  background: linear-gradient(135deg, #9396fb52 0%, #000000 100%);
}

.scene-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  color: var(--text-secondary);
  cursor: pointer;
  transition: transform 0.3s ease;

  padding: 10px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 10px #6365f152;
  background: linear-gradient(135deg, #fff 0%, #9396fb52 100%);
  margin-left: 20px;
}

.scene-group-header:hover {
  transform: translateY(-2px);
}

body.dark .scene-group-header {
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--bg-card) 0%, #9396fb52 100%);
}

.scene-group-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.scene-id {
  font-weight: 600;
  font-size: 12px;
  padding: 4px 10px;
  background: linear-gradient(135deg, #6365f167, #4e46e59b);
  color: #fff;
  border-radius: 6px;
}

.scene-name {
  font-size: 18px;
  font-weight: 600;
  margin-right: auto;
}

/* 增加选择器特异性，确保只应用于场景标题中的元素 */
.scene-group-header .scene-shots-count,
.scene-group-header2 .scene-shots-count {
  margin-left: auto;
  font-size: 14px;
  opacity: 0.8;
}

.scene-group-right {
  display: flex;
  align-items: center;
  gap: 2px;
}

.collapse-icon {
  /* margin-left: auto; */
  transition: transform 0.3s ease;
}

.collapse-icon.is-active {
  transform: rotate(180deg);
}

.scene-content {
  position: relative;
}

/* 瀑布流布局样式 */
.shots-waterfall {
  display: flex;
  gap: 10px;
  width: 100%;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 0;
  /* 确保列可以收缩 */
}

body.dark .scene-content::before {
  background-color: var(--primary-color);
  opacity: 0.4;
}

/* 折叠动画 */
.scene-toggle-enter-active,
.scene-toggle-leave-active {
  transition: all 0.3s ease;
  max-height: 5000px;
  opacity: 1;
  overflow: hidden;
}

.scene-toggle-enter-from,
.scene-toggle-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.shot-card {
  background-color: #fff;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px #6365f152;
  animation: fadeIn 0.3s ease;
}

body.dark .shot-card {
  background-color: var(--bg-card);
}

.shot-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 2px 12px #6365f152;
  border-color: rgba(99, 102, 241, 0.2);
}

.shot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

body.dark .shot-header {
  background-color: var(--bg-tertiary);
  border-color: #e2e8f023;
}

.shot-header-left,
.shot-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.shot-number {
  font-weight: 600;
  padding: 4px 10px;
  background: linear-gradient(135deg, #6366f1cc, #4f46e5cc);
  color: white;
  border-radius: 6px;
  font-size: 14px;
  box-shadow: 0 2px 12px #6365f152;
}

.shot-scene {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  transition: color 0.3s;
}

body.dark .shot-scene {
  color: var(--text-primary);
}

.shot-type {
  color: white;
  background: linear-gradient(135deg, #3b82f6cc, #2563ebcc);
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 13px;
  box-shadow: 0 2px 12px #6365f152;
}

.shot-movement {
  color: #64748b;
  font-size: 13px;
  padding: 4px 10px;
  background-color: #f1f5f9;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

body.dark .shot-movement {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-tertiary);
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #6366f1;
  transition: transform 0.3s ease;
}

.shot-main {
  display: flex;
  flex-direction: row;
  flex: 1;
  border-left: 2px solid #6366f1;
  border-radius: 16px;
  overflow: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.el-tag) {
  cursor: pointer;
}

.shot-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.shot-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  overflow: hidden;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #f8fafc;
  padding: 10px;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

body.dark .detail-group {
  background-color: var(--bg-tertiary);
  border-color: #e2e8f023;
}

.detail-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  border-top: 1px solid #e2e8f0;
  padding-top: 8px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

body.dark .detail-label {
  color: var(--text-tertiary);
  border-color: #e2e8f023;
}

.detail-value {
  font-size: 14px;
  color: #1e293b;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  transition: color 0.3s;
  text-align: left;
}

body.dark .detail-value {
  color: var(--text-secondary);
}

/* 多值标签样式 */
.multi-value-item {
  margin-bottom: 4px;
}

.multi-value-tag {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 6px;
  background-color: #e2e8f0;
  color: #1e293b;
  font-size: 13px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

body.dark .multi-value-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.character-tag {
  background-color: #dbeafe;
  color: #1e40af;
}

body.dark .character-tag {
  background-color: rgba(219, 234, 254, 0.2);
  color: #93c5fd;
}

.action-tag {
  background-color: #dcfce7;
  color: #166534;
}

body.dark .action-tag {
  background-color: rgba(220, 252, 231, 0.1);
  color: #86efac;
}

.emotion-tag {
  background-color: #fef3c7;
  color: #92400e;
}

body.dark .emotion-tag {
  background-color: rgba(254, 243, 199, 0.1);
  color: #fcd34d;
}

.shot-dialogue {
  margin: 0 0 0 0;
  padding: 10px;
  background-color: #f8fafc;
  border-left: 2px solid #6366f1;
  display: flex;
  gap: 16px;
  align-items: center;
  transition: all 0.3s ease;
}

.pangbai {
  width: 96%;
  border-radius: 0px 0px 0px 0px;
}

.duihua {
  border-radius: 0px 0px 12px 12px;
  border-top: 1px solid #e2e8f0;
}

body.dark .duihua {
  border-top: 1px solid #e2e8f023;
}

body.dark .shot-dialogue {
  background-color: var(--bg-tertiary);
}

.dialogue-icon {
  font-size: 14px;
  color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 50%;
}

.dialogue-text {
  flex: 1;
  font-size: 14px;
  color: #1e293b;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0px;
  transition: color 0.3s;
}

body.dark .dialogue-text {
  color: var(--text-secondary);
}

.dialogue-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  transition: all 0.3s ease;
}

body.dark .dialogue-item {
  border-color: var(--border-color);
}

.dialogue-character {
  font-weight: 600;
  color: #3b82f6;
  font-size: 14px;
  min-width: 40px;
  position: relative;
  text-align: left;
  /* padding-left: 18px; */
}

/* 
.dialogue-character::before {
  content: "";
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
} */

body.dark .dialogue-character {
  color: #60a5fa;
}

.dialogue-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: left;
}

.dialogue-action {
  color: #64748b;
  font-size: 13px;
  font-style: italic;
  background-color: #f1f5f9;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

body.dark .dialogue-action {
  color: #94a3b8;
  background-color: rgba(241, 245, 249, 0.1);
}

.dialogue-speech {
  color: #1e293b;
  font-size: 14px;
  line-height: 1.6;
}

body.dark .dialogue-speech {
  color: #e2e8f0;
}

@media (max-width: 480px) {
  .scene-group-right {
    width: 100%;
  }
}

/* 思考链样式适配 */
:deep(.ax-thought-chain) {
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark :deep(.ax-thought-chain) {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.thought-content {
  transition: color 0.3s;
}

body.dark .thought-content {
  color: var(--text-secondary);
}

/* 播放按钮样式 */
.voice-play {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(64, 160, 255, 0.715);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  cursor: pointer;
}

.voice-play:hover {
  transform: scale(1.1);
  background-color: #409eff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.4);
}

.play-icon {
  width: 18px;
  height: 18px;
}

/* 播放状态 */
.voice-play.playing {
  background-color: #67c23a;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.voice-play.playing:hover {
  background-color: #85ce61;
  box-shadow: 0 4px 10px rgba(103, 194, 58, 0.4);
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -14px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 2px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 5px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 8px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 6px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 7px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {

  0%,
  100% {
    transform: scaleY(0.6);
  }

  50% {
    transform: scaleY(1);
  }
}

body.dark .voice-play {
  background-color: rgba(64, 160, 255, 0.3);
}

body.dark .voice-play:hover {
  background-color: rgba(64, 160, 255, 0.8);
}

body.dark .voice-play.playing {
  background-color: rgba(103, 194, 58, 0.6);
}

body.dark .voice-play.playing:hover {
  background-color: rgba(103, 194, 58, 0.8);
}

/* 新的分镜卡片布局样式 */
.shot-image-container {
  /* max-height: 220px; */
  width: 200px;
  flex-shrink: 0;
  position: relative;
  /* 为绝对定位的子元素创建参考点 */
}

/* 分镜ID标签样式 */
.shot-id-tag {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(99, 101, 241, 0.661);
  color: white;
  font-size: 8px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.shot-id-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.shot-content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px 15px;
  /* 调整内边距 */
  background-color: #fff;
  gap: 10px;
  align-items: center;
  justify-content: center;
}

body.dark .shot-content-container {
  background-color: var(--bg-card);
}

/* 删除不再需要的shot-simple-header样式 */
/* .shot-simple-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 10px;
  border-bottom: 1px solid #e2e8f0;
}

body.dark .shot-simple-header {
  border-color: #e2e8f023;
} */

.shot-info-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #f1f5f9;
}

.shot-info-icon:hover {
  background-color: #87b1e9;
  transform: scale(1.1);
}

/* body.dark .shot-info-icon {
  background-color: var(--bg-tertiary);
  color: #818cf8;
} */

/* body.dark .shot-info-icon:hover {
  background-color: var(--bg-secondary);
} */

/* 简化的对话和旁白样式 */
.shot-dialogue.simplified {
  background-color: transparent;
  border-left: none;
  padding: 8px 12px;
  /* margin-bottom: 8px; */
}

.shot-dialogue.pangbai.simplified {
  background-color: #f1f5f9;
  border-radius: 8px;
}

.shot-dialogue.duihua.simplified {
  background-color: #f8fafc;
  border-radius: 8px;
  border-top: none;
}

body.dark .shot-dialogue.pangbai.simplified {
  background-color: var(--bg-tertiary);
}

body.dark .shot-dialogue.duihua.simplified {
  background-color: var(--bg-secondary);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
}

body.dark .detail-label {
  color: var(--text-tertiary);
}

.detail-value {
  font-size: 14px;
  color: #1e293b;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

body.dark .detail-value {
  color: var(--text-secondary);
}

/* 覆盖 Element Plus 对话框样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  padding: 0px;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #6366f1cc, #4f46e5cc);
  border-bottom: none;
}

body.dark :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #4f46e5cc, #3730a3cc);
  border-color: var(--border-color);
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: white;
  letter-spacing: 0.5px;
}

body.dark :deep(.el-dialog__title) {
  color: white;
}

:deep(.el-dialog__body) {
  padding: 0;
  background-color: white;
}

body.dark :deep(.el-dialog__body) {
  background-color: var(--bg-card);
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
  right: 16px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: white;
}

/* 分镜详情对话框内容样式 */
.detail-dialog-content {
  padding-bottom: 16px;
}

.scene-info-bar {
  margin-bottom: 16px;
}

.detail-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.detail-card:last-child {
  margin-bottom: 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .detail-layout {
    flex-direction: column;
  }

  .detail-image-container {
    flex: 0 0 auto;
    width: 100%;
    height: 250px;
    margin-bottom: 16px;
  }

  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}

/* 详情对话框左右布局样式 */
.detail-layout {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
}

.detail-image-container {
  /* flex: 0 0 250px; */
  /* min-width: 300px; */
  flex: 1;
  height: 420px;
  overflow: hidden;
  border-radius: 12px;
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #f8fafc; */
  transition: all 0.3s ease;
}

.detail-image {
  /* width: 100%; */
  height: 100%;
  object-fit: contain;
  object-position: center;
  border-radius: 12px;
}

body.dark .detail-image-container {
  background-color: var(--bg-tertiary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.detail-content {
  /* flex: 1; */
  width: 400px;
  /* 防止内容溢出 */
}

/* 确保图片预览组件在详情对话框中正确显示 */
.detail-image-container :deep(.image-preview) {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.detail-image-container :deep(.image-preview img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* 调整对话框内容样式 */
.detail-dialog-content {
  padding-bottom: 16px;
}

.scene-info-bar {
  margin-bottom: 16px;
}

.detail-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px 20px;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
}

body.dark .detail-card {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .detail-layout {
    flex-direction: column;
  }

  .detail-image-container {
    flex: 0 0 auto;
    width: 100%;
    height: 250px;
    margin-bottom: 16px;
  }

  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}

/* 对话按钮样式 */
.shot-dialogue-button {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;
}

/* 详情对话框中的对话内容样式 */
.dialogue-card {
  border-top: 1px solid #e2e8f0;
}

body.dark .dialogue-card {
  border-top: 1px solid var(--border-color);
}

.dialogue-label {
  margin-bottom: 12px;
  color: #4f46e5;
}

body.dark .dialogue-label {
  color: #818cf8;
}

.dialogue-content-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-left: 26px;
  /* 左侧对齐图标 */
}

/* 详情对话框中的对话项样式 */
.dialogue-content-container .dialogue-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

body.dark .dialogue-content-container .dialogue-item {
  background-color: var(--bg-tertiary);
}

.dialogue-content-container .dialogue-character {
  font-weight: 600;
  color: #3b82f6;
  font-size: 14px;
}

body.dark .dialogue-content-container .dialogue-character {
  color: #60a5fa;
}

.dialogue-content-container .dialogue-content {
  font-size: 14px;
  color: #1e293b;
  line-height: 1.6;
}

body.dark .dialogue-content-container .dialogue-content {
  color: var(--text-secondary);
}

/* 场景信息栏样式 */
.scene-info-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

body.dark .scene-info-bar {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.scene-info-label {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  margin-right: 8px;
}

body.dark .scene-info-label {
  color: var(--text-tertiary);
}

.scene-info-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 500;
}

body.dark .scene-info-value {
  color: var(--text-secondary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .shots-waterfall {
    gap: 8px;
  }

  .shot-main {
    flex-direction: column;
  }

  .shot-image-container {
    width: 100%;
    max-height: none;
  }

  .shot-content-container {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .shots-waterfall {
    gap: 6px;
  }

}

/* 分镜完成状态指示器样式 */
.scene-completion-indicator {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.scene-completion-indicator.complete {
  background-color: #67C23A;
  color: white;
}

.scene-completion-indicator.partial {
  background-color: #E6A23C;
  color: white;
}

.scene-completion-indicator.incomplete {
  background-color: #F56C6C;
  color: white;
}

body.dark .scene-completion-indicator.complete {
  background-color: rgba(103, 194, 58, 0.8);
}

body.dark .scene-completion-indicator.partial {
  background-color: rgba(230, 162, 60, 0.8);
}

body.dark .scene-completion-indicator.incomplete {
  background-color: rgba(245, 108, 108, 0.8);
}

/* 添加新的Grid布局样式 */
.chapters-grid-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 10px;
  padding: 10px;
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .chapters-grid-container {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .book-cover.has-cover-image .chapter-title {
    font-size: 18px;
    margin-top: 30px;
  }
}


/* 书籍样式的章节卡片 */
.chapter-card {
  position: relative;
  height: 320px;
  border-radius: 4px;
  overflow: visible;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  perspective: 1200px;
  margin: 15px 50px 25px 10px;
  transform: scale(0.8);
}

/* 交错布局效果 */
.offset-card {
  margin-top: 20px;
}

.chapter-card:hover {
  transform: translateY(-8px);
}

/* 随机旋转效果 - 让书本看起来更自然 */
.chapter-card:nth-child(3n) {
  transform: rotate(1deg);
}

.chapter-card:nth-child(3n+1) {
  transform: rotate(-1deg);
}

.chapter-card:nth-child(3n+2) {
  transform: rotate(0.5deg);
}

/* 悬停时重置旋转并上移 */
.chapter-card:hover {
  transform: translateY(-8px) rotate(0) !important;
}

.book-spine {
  position: absolute;
  left: 0;
  top: 0;
  width: 15px;
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #6366f1);
  border-radius: 4px 0 0 4px;
  box-shadow: -1px 0 5px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

/* 为不同的书本添加不同的书脊颜色 */
.chapter-card:nth-child(4n) .book-spine {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.chapter-card:nth-child(4n+1) .book-spine {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.chapter-card:nth-child(4n+2) .book-spine {
  background: linear-gradient(90deg, #ef4444, #f87171);
}

.book-cover {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  /* padding: 20px; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transform-origin: left center;
  transition: transform 0.5s cubic-bezier(0.25, 1, 0.5, 1);
  z-index: 1;
  background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  border: 1px solid #e2e8f0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
}

/* 有封面图片的样式 */
.book-cover.has-cover-image {
  background-image: none;
  /* 移除默认的渐变背景 */
  padding: 20px;
}

.book-cover.has-cover-image .chapter-title,
.book-cover.has-cover-image .chapter-stats,
.book-cover.has-cover-image .stat-value {
  position: relative;
  z-index: 2;
  color: #1e293b;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

body.dark .book-cover.has-cover-image .chapter-title,
body.dark .book-cover.has-cover-image .chapter-stats,
body.dark .book-cover.has-cover-image .stat-value {
  color: #e2e8f0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.361), rgb(255, 255, 255));
  border-radius: 4px;
  z-index: 1;
}

body.dark .cover-overlay {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.75));
}

body.dark .book-cover {
  background-image: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  border-color: #4a5568;
  color: #e2e8f0;
}

.chapter-card:hover .book-cover {
  transform: rotateY(-8deg);
  box-shadow: 8px 8px 20px rgba(0, 0, 0, 0.2);
}

/* 添加书本纹理效果 */
.book-cover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm32-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 1;
  border-radius: 4px;
  z-index: -1;
  pointer-events: none;
}

/* 有封面图片时隐藏纹理 */
.book-cover.has-cover-image::before {
  display: none;
}

.chapter-number {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #4f46e5;
  color: white;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 2;
}

/* 根据书脊颜色调整章节编号背景色 */
.chapter-card:nth-child(4n) .chapter-number {
  background-color: #10b981;
}

.chapter-card:nth-child(4n+1) .chapter-number {
  background-color: #f59e0b;
}

.chapter-card:nth-child(4n+2) .chapter-number {
  background-color: #ef4444;
}

.chapter-title {
  font-size: 20px;
  font-weight: bold;
  color: #1e293b;
  margin-top: 40px;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

body.dark .chapter-title {
  color: #e2e8f0;
}

.chapter-stats {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #e2e8f0;
  color: #4b5563;
}

.stat-icon.completed {
  background-color: #10b981;
  color: white;
}

body.dark .stat-icon {
  background-color: #4a5568;
  color: #e2e8f0;
}

body.dark .stat-icon.completed {
  background-color: #059669;
}

.stat-value {
  font-size: 14px;
  color: #4b5563;
}

body.dark .stat-value {
  color: #a0aec0;
}

/* 分镜内容视图样式 */
.scene-content-view {
  animation: fadeIn 0.3s ease;
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #4b5563;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 6px;
  width: fit-content;
}

.back-button:hover {
  background-color: #f1f5f9;
  color: #1e293b;
  transform: translateX(-5px);
}

body.dark .back-button {
  background-color: #1a202c;
  border-color: #4a5568;
  color: #a0aec0;
}

body.dark .back-button:hover {
  background-color: #2d3748;
  color: #e2e8f0;
}

.selected-chapter-header {
  flex: 1;
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  margin-bottom: 8px;
  padding: 6px 10px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  gap: 10px;
}

body.dark .selected-chapter-header {
  background-color: #1a202c;
  border-color: #4a5568;
}

@media (max-width: 768px) {
  .scene-content-view {
    flex-direction: column;
  }
  .selected-chapter-header {
    flex: 0;
  }
}

.selected-chapter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  padding: 2px;
  flex: 1;
  text-align: left;
}

body.dark .selected-chapter-title {
  color: #e2e8f0;
}

.chapter-actions {
  display: flex;
  gap: 10px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scene-toggle-enter-active,
.scene-toggle-leave-active {
  transition: all 0.3s ease;
}

.scene-toggle-enter-from,
.scene-toggle-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 隐藏预加载图片 */
.hidden-preload-image {
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
  z-index: -1;
}

/* 高级创作按钮样式 */
.advanced-creation-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: #4f46e5;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.4);
  transition: all 0.3s ease;
  z-index: 10;
}

.advanced-creation-button:hover {
  background-color: #4338ca;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.5);
}

body.dark .advanced-creation-button {
  background-color: #6366f1;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.4);
}

body.dark .advanced-creation-button:hover {
  background-color: #818cf8;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.5);
}

/* 确认对话框样式 */
.confirm-dialog-content {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.confirm-dialog-content p {
  font-size: 16px;
  line-height: 1.5;
  color: #374151;
  margin-bottom: 16px;
  font-weight: 500;
}

body.dark .confirm-dialog-content p {
  color: #e5e7eb;
}

.confirm-dialog-icon {
  font-size: 36px;
  color: #f59e0b;
  margin-top: 5px;
}

body.dark .confirm-dialog-icon {
  color: #fbbf24;
}

.confirm-dialog-message p {
  flex: 1;
  text-align: center;
  font-size: 20px;
}

.confirm-dialog-options {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  margin-top: 10px;
}

body.dark .confirm-dialog-options {
  background-color: #1e293b;
}

.option-item {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e2e8f0;
}

.option-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

body.dark .option-item {
  border-color: #334155;
}

.option-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

body.dark .option-title {
  color: #e2e8f0;
}

.option-desc {
  font-size: 14px;
  color: #64748b;
}

body.dark .option-desc {
  color: #94a3b8;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

/* 自定义按钮样式 */
.keep-button {
  background-color: #10b981;
  border-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.keep-button:hover {
  background-color: #059669;
  border-color: #059669;
  transform: translateY(-2px);
}

body.dark .keep-button {
  background-color: #059669;
  border-color: #059669;
}

body.dark .keep-button:hover {
  background-color: #10b981;
  border-color: #10b981;
}

.overwrite-button {
  background-color: #f59e0b;
  border-color: #f59e0b;
  color: white;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.overwrite-button:hover {
  background-color: #d97706;
  border-color: #d97706;
  transform: translateY(-2px);
}

body.dark .overwrite-button {
  background-color: #d97706;
  border-color: #d97706;
}

body.dark .overwrite-button:hover {
  background-color: #f59e0b;
  border-color: #f59e0b;
}

/* 对话框自定义样式 */
:deep(.canvas-confirm-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.canvas-confirm-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #4f46e5, #6366f1);
  padding: 16px 20px;
}

body.dark :deep(.canvas-confirm-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #4338ca, #4f46e5);
}

:deep(.canvas-confirm-dialog .el-dialog__title) {
  color: white;
  font-weight: 600;
}

:deep(.canvas-confirm-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.canvas-confirm-dialog .el-dialog__headerbtn:hover .el-dialog__close) {
  color: white;
}

/* 添加章节卡片样式 */
.add-chapter-card {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  border: 2px dashed #e2e8f0;
  background-color: rgba(255, 255, 255, 0.5);
  transform: scale(0.8);
}

body.dark .add-chapter-card {
  border-color: #4a5568;
  background-color: rgba(26, 32, 44, 0.5);
}

.add-chapter-card:hover {
  transform: translateY(-8px) scale(0.85) !important;
  border-color: #6366f1;
}

body.dark .add-chapter-card:hover {
  border-color: #818cf8;
}

.add-chapter-spine {
  background: linear-gradient(90deg, #6366f1, #818cf8);
}

.add-chapter-cover {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-image: none !important;
  background-color: transparent !important;
  box-shadow: none;
}

.add-chapter-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #6366f1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.add-chapter-card:hover .add-chapter-icon {
  transform: scale(1.1) rotate(90deg);
  background-color: #4f46e5;
}

body.dark .add-chapter-icon {
  background-color: #4f46e5;
}

body.dark .add-chapter-card:hover .add-chapter-icon {
  background-color: #6366f1;
}

.add-chapter-desc {
  font-size: 14px;
  color: #64748b;
  margin-top: 10px;
  text-align: center;
}

body.dark .add-chapter-desc {
  color: #94a3b8;
}

/* 添加章节对话框样式 */
:deep(.add-chapter-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

:deep(.add-chapter-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  padding: 18px 24px;
  margin: 0;
}

:deep(.add-chapter-dialog .el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
  letter-spacing: 0.5px;
}

:deep(.add-chapter-dialog .el-dialog__headerbtn) {
  top: 18px;
  right: 20px;
}

:deep(.add-chapter-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.9);
  font-size: 18px;
}

:deep(.add-chapter-dialog .el-dialog__body) {
  padding: 24px;
  background-color: #f8fafc;
}

body.dark :deep(.add-chapter-dialog .el-dialog__body) {
  background-color: #1a202c;
}

.add-chapter-dialog-content {
  padding: 0;
}

.add-chapter-description {
  margin-bottom: 24px;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #6366f1;
}

body.dark .add-chapter-description {
  background-color: #2d3748;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-left: 4px solid #818cf8;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

body.dark .description-title {
  color: #e2e8f0;
}

.description-subtitle {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

body.dark .description-subtitle {
  color: #94a3b8;
}

.chapter-input {
  margin-bottom: 10px;
}

:deep(.chapter-input .el-textarea__inner) {
  font-size: 16px;
  line-height: 1.6;
  padding: 16px;
  border-radius: 12px;
  resize: none;
  transition: all 0.3s ease;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background-color: white;
  height: 180px !important;
}

body.dark :deep(.chapter-input .el-textarea__inner) {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:deep(.chapter-input .el-textarea__inner:focus) {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

body.dark :deep(.chapter-input .el-textarea__inner:focus) {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2);
}

:deep(.add-chapter-dialog .el-dialog__footer) {
  padding: 16px 24px;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

body.dark :deep(.add-chapter-dialog .el-dialog__footer) {
  background-color: #1a202c;
  border-top: 1px solid #4a5568;
}

.cancel-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.confirm-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background-color: #6366f1;
  border-color: #6366f1;
  display: flex;
  align-items: center;
  gap: 6px;
}

.confirm-button:hover {
  background-color: #4f46e5;
  border-color: #4f46e5;
  transform: translateY(-2px);
}

body.dark .confirm-button {
  background-color: #818cf8;
  border-color: #818cf8;
}

body.dark .confirm-button:hover {
  background-color: #6366f1;
  border-color: #6366f1;
}

/* 章节详情按钮样式 */
.chapter-detail-button {
  position: absolute;
  top: 10px;
  left: 24px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 10;
}

.chapter-detail-button:hover {
  transform: scale(1.1);
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

body.dark .chapter-detail-button {
  background-color: rgba(30, 41, 59, 0.9);
  color: #818cf8;
}

body.dark .chapter-detail-button:hover {
  background-color: #1e293b;
}
</style>